import requests
import json
import pymysql
import time
from datetime import datetime
from typing import List, Dict, Optional
import logging
import random
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WalmartReturnDataProcessor:
    """沃尔玛退款数据处理器"""

    def __init__(self, authorization_token: str, db_config: Dict = None, max_retries: int = 3, retry_delay: float = 1.0):
        """
        初始化处理器

        Args:
            authorization_token: API授权令牌
            db_config: 数据库配置字典
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
        """
        self.authorization_token = authorization_token
        self.db_config = db_config or {}
        self.base_url = 'https://walmart-api.eccang.com/v1/api/order-returns/list'
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.session = self._create_session()

    def _create_session(self) -> requests.Session:
        """创建会话对象"""
        session = requests.Session()

        # 设置连接池参数
        session.mount('http://', requests.adapters.HTTPAdapter(pool_connections=10, pool_maxsize=20))
        session.mount('https://', requests.adapters.HTTPAdapter(pool_connections=10, pool_maxsize=20))

        return session

    def _get_headers(self) -> Dict:
        """获取请求头"""
        return {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'authorization': f'Bearer {self.authorization_token}',
            'cache-control': 'no-cache',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://home.eccang.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://home.eccang.com/',
            'resource': 'usercenter',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'user_code': 'eccang',
        }

    def fetch_return_data(self,
                         start_date: str,
                         end_date: str,
                         page: int = 1,
                         page_size: int = 500,
                         sites: List = None,
                         accounts: List = None,
                         return_reasons: List = None) -> Dict:
        """
        获取退款数据（带重试机制）

        Args:
            start_date: 开始日期 (格式: 'YYYY-MM-DD HH:MM:SS')
            end_date: 结束日期 (格式: 'YYYY-MM-DD HH:MM:SS')
            page: 页码
            page_size: 每页大小（默认500）
            sites: 站点列表
            accounts: 账户列表
            return_reasons: 退款原因列表

        Returns:
            API响应数据
        """
        json_data = {
            'page': page,
            'sites': sites or [],
            'accounts': accounts or [],
            'search_field': 'customer_order_id',
            'return_reasons': return_reasons or [],
            'search_date_start': start_date,
            'search_date_end': end_date,
            'page_size': page_size,
        }

        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"尝试第 {attempt + 1} 次请求第 {page} 页数据...")

                response = self.session.post(
                    self.base_url,
                    headers=self._get_headers(),
                    json=json_data,
                    timeout=30  # 添加超时设置
                )
                response.raise_for_status()

                result = response.json()

                # 检查API返回的状态码
                if result.get('code') != 200:
                    error_msg = result.get('message', '未知错误')
                    logger.warning(f"API返回错误状态码: {result.get('code')}, 消息: {error_msg}")

                    # 如果是认证错误或权限错误，不重试
                    if result.get('code') in [401, 403]:
                        raise requests.exceptions.HTTPError(f"认证失败: {error_msg}")

                    # 其他错误可以重试
                    if attempt < self.max_retries:
                        continue
                    else:
                        raise requests.exceptions.HTTPError(f"API错误: {error_msg}")

                logger.debug(f"第 {page} 页数据请求成功")
                return result

            except (requests.exceptions.RequestException, requests.exceptions.Timeout) as e:
                logger.warning(f"第 {attempt + 1} 次请求失败: {e}")

                if attempt < self.max_retries:
                    # 指数退避策略
                    delay = self.retry_delay * (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"等待 {delay:.2f} 秒后重试...")
                    time.sleep(delay)
                else:
                    logger.error(f"API请求最终失败，已重试 {self.max_retries} 次: {e}")
                    raise
            except Exception as e:
                logger.error(f"请求过程中发生未知错误: {e}")
                raise


    def fetch_all_pages(self,
                       start_date: str,
                       end_date: str,
                       page_size: int = 500,
                       sites: List = None,
                       accounts: List = None,
                       return_reasons: List = None,
                       max_pages: int = None,
                       delay_between_pages: float = 1.0) -> List[Dict]:
        """
        获取所有页面的数据（优化版本）

        Args:
            start_date: 开始日期
            end_date: 结束日期
            page_size: 每页大小（默认500）
            sites: 站点列表
            accounts: 账户列表
            return_reasons: 退款原因列表
            max_pages: 最大页数限制
            delay_between_pages: 页面间延迟时间（秒）

        Returns:
            所有数据的列表
        """
        all_data = []
        page = 1
        total_records = 0
        consecutive_failures = 0
        max_consecutive_failures = 3

        logger.info(f"开始获取分页数据，每页 {page_size} 条记录")

        while True:
            # 检查最大页数限制
            if max_pages and page > max_pages:
                logger.info(f"已达到最大页数限制 {max_pages}，停止获取")
                break

            # 检查连续失败次数
            if consecutive_failures >= max_consecutive_failures:
                logger.error(f"连续失败 {consecutive_failures} 次，停止获取")
                break

            logger.info(f"正在获取第 {page} 页数据...")

            try:
                response_data = self.fetch_return_data(
                    start_date=start_date,
                    end_date=end_date,
                    page=page,
                    page_size=page_size,
                    sites=sites,
                    accounts=accounts,
                    return_reasons=return_reasons
                )

                # 重置连续失败计数器
                consecutive_failures = 0

                # 检查响应状态
                if response_data.get('code') != 200:
                    error_msg = response_data.get('message', '未知错误')
                    logger.error(f"API返回错误: {error_msg}")
                    break

                # 获取数据
                data_info = response_data.get('data', {})
                data_list = data_info.get('list', [])
                total_records = data_info.get('total', 0)

                # 检查是否有数据
                if not data_list:
                    logger.info("当前页没有数据，停止获取")
                    break

                # 添加数据到结果集
                all_data.extend(data_list)
                current_count = len(all_data)

                logger.info(f"第 {page} 页获取到 {len(data_list)} 条记录")
                logger.info(f"累计获取 {current_count} 条记录，总记录数 {total_records}")

                # 检查是否已获取所有数据
                if current_count >= total_records:
                    logger.info("已获取所有数据")
                    break

                # 检查当前页数据是否少于页面大小（可能是最后一页）
                if len(data_list) < page_size:
                    logger.info("当前页数据少于页面大小，可能已到最后一页")
                    break

                page += 1

                # 页面间延迟，避免请求过于频繁
                if delay_between_pages > 0:
                    logger.debug(f"等待 {delay_between_pages} 秒后请求下一页...")
                    time.sleep(delay_between_pages)

            except Exception as e:
                consecutive_failures += 1
                logger.error(f"获取第 {page} 页数据失败 (第 {consecutive_failures} 次连续失败): {e}")

                # 如果不是最后一次尝试，等待一段时间后继续
                if consecutive_failures < max_consecutive_failures:
                    wait_time = consecutive_failures * 2  # 递增等待时间
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

        logger.info(f"分页数据获取完成，总计 {len(all_data)} 条记录")
        return all_data

    def flatten_walmart_return_data(self, json_data):
        """
        将沃尔玛退款数据进行扁平化处理
        将每个订单的产品信息拆分成独立的记录
        """
        flattened_data = []

        # 解析JSON数据
        if isinstance(json_data, str):
            data = json.loads(json_data)
        else:
            data = json_data

        # 获取订单列表
        if isinstance(json_data, list):
            orders = json_data
        else:
            orders = data.get('data', {}).get('list', [])

        for order in orders:
            # 提取订单基本信息
            base_info = {
                'account': order.get('account', ''),
                'account_alias': order.get('account_alias', ''),
                'return_order_id': order.get('return_order_id', ''),
                'customer_email_id': order.get('customer_email_id', ''),
                'customer_name': order.get('customer_name', ''),
                'customer_order_id': order.get('customer_order_id', ''),
                'refund_mode': order.get('refund_mode', ''),
                'return_type': order.get('return_type', ''),
                'total_refund_amount': order.get('total_refund_amount', ''),
                'return_order_date': order.get('return_order_date', '')
            }

            # 获取产品列表
            products = order.get('order_returns_product', [])

            # 如果没有产品信息，也要保留订单基本信息
            if not products:
                flattened_record = base_info.copy()
                flattened_record.update({
                    'item_sku': '',
                    'refunded_qty': 0,
                    'cancellable_qty': 0,
                    'refund_line_total_amount': '',
                    'refund_line_total_amount_currency': '',
                    'return_reason': '',
                    'return_status': '',
                    'current_refund_status': '',
                    'return_cancellation_reason': ''
                })
                flattened_data.append(flattened_record)
            else:
                # 为每个产品创建一条记录
                for product in products:
                    flattened_record = base_info.copy()
                    flattened_record.update({
                        'item_sku': product.get('item_sku', ''),
                        'refunded_qty': product.get('refunded_qty', 0),
                        'cancellable_qty': product.get('cancellable_qty', 0),
                        'refund_line_total_amount': product.get('refund_line_total_amount', ''),
                        'refund_line_total_amount_currency': product.get('refund_line_total_amount_currency', ''),
                        'return_reason': product.get('return_reason', ''),
                        'return_status': product.get('return_status', ''),
                        'current_refund_status': product.get('current_refund_status', ''),
                        'return_cancellation_reason': product.get('return_cancellation_reason', '')
                    })
                    flattened_data.append(flattened_record)

        return flattened_data



    def save_to_database(self, flattened_data: List[Dict], batch_size: int = 50):
        """
        将扁平化数据保存到数据库（优化版本）

        Args:
            flattened_data: 扁平化的数据列表
            batch_size: 批量插入大小（减小批次大小避免超时）
        """
        if not self.db_config:
            logger.warning("未配置数据库，跳过保存")
            return

        if not flattened_data:
            logger.info("没有数据需要保存")
            return

        logger.info(f"开始保存 {len(flattened_data)} 条记录到数据库，批次大小: {batch_size}")

        insert_sql = """
        INSERT INTO sgm_walmart_refund_order (
            account, account_alias, return_order_id, customer_email_id, customer_name,
            customer_order_id, refund_mode, return_type, total_refund_amount, return_order_date,
            item_sku, refunded_qty, cancellable_qty, refund_line_total_amount,
            refund_line_total_amount_currency, return_reason, return_status,
            current_refund_status, return_cancellation_reason
        ) VALUES (
            %(account)s, %(account_alias)s, %(return_order_id)s, %(customer_email_id)s, %(customer_name)s,
            %(customer_order_id)s, %(refund_mode)s, %(return_type)s, %(total_refund_amount)s, %(return_order_date)s,
            %(item_sku)s, %(refunded_qty)s, %(cancellable_qty)s, %(refund_line_total_amount)s,
            %(refund_line_total_amount_currency)s, %(return_reason)s, %(return_status)s,
            %(current_refund_status)s, %(return_cancellation_reason)s
        ) ON DUPLICATE KEY UPDATE
            account = VALUES(account),
            account_alias = VALUES(account_alias),
            customer_email_id = VALUES(customer_email_id),
            customer_name = VALUES(customer_name),
            customer_order_id = VALUES(customer_order_id),
            refund_mode = VALUES(refund_mode),
            return_type = VALUES(return_type),
            total_refund_amount = VALUES(total_refund_amount),
            return_order_date = VALUES(return_order_date),
            item_sku = VALUES(item_sku),
            refunded_qty = VALUES(refunded_qty),
            cancellable_qty = VALUES(cancellable_qty),
            refund_line_total_amount = VALUES(refund_line_total_amount),
            refund_line_total_amount_currency = VALUES(refund_line_total_amount_currency),
            return_reason = VALUES(return_reason),
            return_status = VALUES(return_status),
            current_refund_status = VALUES(current_refund_status),
            return_cancellation_reason = VALUES(return_cancellation_reason),
            update_datetime = CURRENT_TIMESTAMP(6)
        """

        connection = None
        try:
            # 添加连接超时设置
            db_config_with_timeout = self.db_config.copy()
            db_config_with_timeout.update({
                'connect_timeout': 30,
                'read_timeout': 120,  # 增加读取超时时间
                'write_timeout': 120  # 增加写入超时时间
            })

            connection = pymysql.connect(**db_config_with_timeout)
            logger.info("数据库连接成功")

            # 数据预处理
            processed_data = []
            for record in flattened_data:
                processed_record = record.copy()

                # 处理金额字段
                for amount_field in ['total_refund_amount', 'refund_line_total_amount']:
                    if processed_record.get(amount_field):
                        try:
                            # 移除货币符号并转换为浮点数
                            amount_str = str(processed_record[amount_field]).replace('$', '').replace(',', '')
                            processed_record[amount_field] = float(amount_str) if amount_str else 0.0
                        except (ValueError, TypeError):
                            processed_record[amount_field] = 0.0
                    else:
                        processed_record[amount_field] = 0.0

                # 处理日期字段
                if processed_record.get('return_order_date'):
                    try:
                        # 尝试解析日期
                        date_str = processed_record['return_order_date']
                        if isinstance(date_str, str):
                            processed_record['return_order_date'] = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                    except (ValueError, TypeError):
                        processed_record['return_order_date'] = None

                processed_data.append(processed_record)

            # 批量插入（优化版本）
            total_inserted = 0
            total_failed = 0

            with connection.cursor() as cursor:
                for i in range(0, len(processed_data), batch_size):
                    batch = processed_data[i:i + batch_size]
                    batch_num = i//batch_size + 1

                    logger.info(f"正在处理第 {batch_num} 批，共 {len(batch)} 条记录...")

                    try:
                        # 设置较短的查询超时时间
                        cursor.execute("SET SESSION wait_timeout = 300")
                        cursor.execute("SET SESSION interactive_timeout = 300")

                        affected_rows = cursor.executemany(insert_sql, batch)
                        connection.commit()

                        batch_inserted = len(batch)
                        total_inserted += batch_inserted

                        logger.info(f"✅ 第 {batch_num} 批成功插入 {batch_inserted} 条记录")

                    except Exception as e:
                        logger.error(f"❌ 第 {batch_num} 批批量插入失败: {e}")
                        connection.rollback()

                        # 尝试逐条插入
                        logger.info(f"🔄 尝试逐条插入第 {batch_num} 批的 {len(batch)} 条记录...")
                        for j, record in enumerate(batch, 1):
                            try:
                                cursor.execute(insert_sql, record)
                                connection.commit()
                                total_inserted += 1

                                if j % 10 == 0:  # 每10条记录显示一次进度
                                    logger.info(f"   逐条插入进度: {j}/{len(batch)}")

                            except Exception as single_error:
                                total_failed += 1
                                logger.warning(f"   插入单条记录失败: {single_error}")
                                connection.rollback()

                    # 批次间稍作延迟，避免数据库压力过大
                    if i + batch_size < len(processed_data):
                        time.sleep(0.1)

            logger.info(f"✅ 数据保存完成！成功插入 {total_inserted} 条记录，失败 {total_failed} 条")

        except KeyboardInterrupt:
            logger.warning("⚠️ 用户中断操作，正在安全关闭数据库连接...")
            if connection:
                connection.rollback()
            raise
        except Exception as e:
            logger.error(f"❌ 保存数据到数据库失败: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                try:
                    connection.close()
                    logger.info("数据库连接已关闭")
                except:
                    pass

    def process_and_save(self,
                        start_date: str,
                        end_date: str,
                        page_size: int = 500,
                        sites: List = None,
                        accounts: List = None,
                        return_reasons: List = None,
                        max_pages: int = None,
                        save_to_db: bool = True,
                        delay_between_pages: float = 1.0) -> List[Dict]:
        """
        完整的数据处理流程：获取数据 -> 扁平化 -> 保存到数据库

        Args:
            start_date: 开始日期
            end_date: 结束日期
            page_size: 每页大小（默认500）
            sites: 站点列表
            accounts: 账户列表
            return_reasons: 退款原因列表
            max_pages: 最大页数限制
            save_to_db: 是否保存到数据库
            delay_between_pages: 页面间延迟时间（秒）

        Returns:
            扁平化的数据列表
        """
        logger.info(f"开始处理沃尔玛退款数据: {start_date} 到 {end_date}")

        # 1. 获取所有数据
        all_data = self.fetch_all_pages(
            start_date=start_date,
            end_date=end_date,
            page_size=page_size,
            sites=sites,
            accounts=accounts,
            return_reasons=return_reasons,
            max_pages=max_pages,
            delay_between_pages=delay_between_pages
        )

        if not all_data:
            logger.info("没有获取到数据")
            return []

        # 2. 扁平化数据
        logger.info("开始扁平化数据...")
        flattened_data = self.flatten_walmart_return_data(all_data)
        logger.info(f"扁平化完成，共 {len(flattened_data)} 条记录")

        # 3. 保存到数据库
        if save_to_db and self.db_config:
            logger.info("开始保存到数据库...")
            self.save_to_database(flattened_data)

        return flattened_data

    def fetch_data_by_time_range(self,
                                start_date: str,
                                end_date: str,
                                page_size: int = 500,
                                save_to_db: bool = True,
                                delay_between_pages: float = 1.0) -> List[Dict]:
        """
        根据时间范围抓取所有分页数据的便捷方法

        Args:
            start_date: 开始日期 (格式: 'YYYY-MM-DD HH:MM:SS')
            end_date: 结束日期 (格式: 'YYYY-MM-DD HH:MM:SS')
            page_size: 每页大小（默认500）
            save_to_db: 是否保存到数据库
            delay_between_pages: 页面间延迟时间（秒）

        Returns:
            扁平化的数据列表
        """
        logger.info(f"开始根据时间范围抓取数据: {start_date} 到 {end_date}")
        logger.info(f"配置: 页面大小={page_size}, 页面间延迟={delay_between_pages}秒")

        return self.process_and_save(
            start_date=start_date,
            end_date=end_date,
            page_size=page_size,
            sites=None,  # 获取所有站点
            accounts=None,  # 获取所有账户
            return_reasons=None,  # 获取所有退款原因
            max_pages=None,  # 不限制页数，获取所有数据
            save_to_db=save_to_db,
            delay_between_pages=delay_between_pages
        )

def get_first_day_of_months_ago(months):
    today = datetime.now()
    # 计算months个月前的年份和月份
    for _ in range(months):
        today = today.replace(day=1) - timedelta(days=1)
    return today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

def main():
    """主函数示例"""

    # 配置参数
    AUTHORIZATION_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************-U1NPX1NZU19VU0VSIn0.fVDdrHsTn9R0udn2PYPWK4vGnhyCgNu8_OCZQExUQEA"

    # 数据库配置
    DB_CONFIG = {
        'host': '*************',
        'port': 3306,
        'user': 'sgm',
        'password': 'edfp.md4321',
        'database': 'segmart_erp',
        'autocommit': True,
        'charset': 'utf8mb4'
    }




    # 查询参数
    # 获取2个月前的第一天起始时间(00:00:00)
    START_DATE = get_first_day_of_months_ago(2).strftime('%Y-%m-%d %H:%M:%S')  # 前2个月第一天
    # 获取当前日期的结束时间(23:59:59)
    END_DATE = datetime.now().strftime('%Y-%m-%d 23:59:59')  # 当前日期结束时间


    PAGE_SIZE = 500  # 优化后的页面大小

    try:
        # 创建处理器实例（带重试配置）
        processor = WalmartReturnDataProcessor(
            authorization_token=AUTHORIZATION_TOKEN,
            db_config=DB_CONFIG,
            max_retries=3,  # 最大重试次数
            retry_delay=1.0  # 重试延迟时间
        )

        # 处理数据
        flattened_data = processor.process_and_save(
            start_date=START_DATE,
            end_date=END_DATE,
            page_size=PAGE_SIZE,
            max_pages=None,  # 不限制页数，获取所有数据
            save_to_db=True,  # 保存到数据库
            delay_between_pages=1.0  # 页面间延迟1秒
        )

        # 显示结果
        logger.info(f"处理完成！共获取 {len(flattened_data)} 条扁平化记录")

    except Exception as e:
        logger.error(f"处理失败: {e}")


if __name__ == "__main__":
    main()