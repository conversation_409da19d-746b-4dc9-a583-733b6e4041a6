# 沃尔玛退款数据处理器 - 优化版

## 功能说明

这是一个功能完整的沃尔玛退款数据处理工具，支持API数据获取、扁平化处理和数据库存储。

### 🚀 主要功能
- **参数化查询**：支持自定义日期范围、页码、页大小、授权令牌等参数
- **分页获取**：自动获取所有页面的数据，支持大量数据处理
- **数据扁平化**：将嵌套的JSON数据转换为平面表格结构
- **数据库存储**：支持MySQL数据库自动建表和数据存储
- **错误处理**：完整的异常处理和日志记录
- **批量处理**：支持大数据量的批量插入和更新
- **数据验证**：自动处理数据类型转换和格式验证

## 📁 文件说明

### 核心文件
- `沃尔玛退款数据.py` - **主要处理器**，包含完整的数据处理逻辑
- `优化版使用示例.py` - **使用示例**，展示各种使用场景
- `config_example.py` - **配置示例**，包含所有配置参数说明
- `requirements.txt` - **依赖包列表**

### 旧版文件（兼容性保留）
- `数据扁平化处理.py` - 原始的数据处理逻辑
- `使用示例.py` - 简化的使用示例

### 配置和文档
- `README.md` - 详细使用说明
- `config_example.py` - 配置文件模板

## 数据字段说明

### 订单基本信息（保持不变）
- `account` - 账户名称
- `account_alias` - 账户别名
- `return_order_id` - 退款订单ID
- `customer_email_id` - 客户邮箱ID
- `customer_name` - 客户姓名
- `customer_order_id` - 客户订单ID
- `refund_mode` - 退款模式
- `return_type` - 退款类型
- `total_refund_amount` - 总退款金额
- `return_order_date` - 退款订单日期

### 产品信息（每个产品一条记录）
- `item_sku` - 产品SKU
- `refunded_qty` - 退款数量
- `cancellable_qty` - 可取消数量
- `refund_line_total_amount` - 单行退款总金额
- `refund_line_total_amount_currency` - 退款货币
- `return_reason` - 退款原因
- `return_status` - 退款状态
- `current_refund_status` - 当前退款状态
- `return_cancellation_reason` - 退款取消原因

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 基础使用（不使用数据库）
```python
from 沃尔玛退款数据 import WalmartReturnDataProcessor

# 创建处理器实例
processor = WalmartReturnDataProcessor(
    authorization_token="your_token_here",
    db_config=None  # 不使用数据库
)

# 获取并处理数据
flattened_data = processor.process_and_save(
    start_date='2025-01-01 00:00:00',
    end_date='2025-01-07 23:59:59',
    page_size=100,
    save_to_db=False
)

print(f"获取到 {len(flattened_data)} 条记录")
```

### 3. 使用数据库存储
```python
# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'your_username',
    'password': 'your_password',
    'database': 'walmart_data',
    'charset': 'utf8mb4'
}

# 创建处理器实例
processor = WalmartReturnDataProcessor(
    authorization_token="your_token_here",
    db_config=db_config
)

# 获取数据并保存到数据库
flattened_data = processor.process_and_save(
    start_date='2025-01-01 00:00:00',
    end_date='2025-01-07 23:59:59',
    save_to_db=True
)
```

### 4. 运行示例
```bash
# 运行所有示例
python 优化版使用示例.py

# 或者运行主脚本
python 沃尔玛退款数据.py
```

## 数据转换示例

### 原始数据结构
```json
{
  "data": {
    "list": [
      {
        "account": "CoCLUB",
        "return_order_id": "163669365382901624",
        "customer_name": "heather soulchin",
        "total_refund_amount": "169.99",
        "order_returns_product": [
          {
            "item_sku": "JAEWe-W50459220-FullBed01",
            "refunded_qty": 1,
            "refund_line_total_amount": "169.99",
            "return_reason": "INADEQUATE_QUALITY"
          }
        ]
      }
    ]
  }
}
```

### 扁平化后的数据
| account | return_order_id | customer_name | total_refund_amount | item_sku | refunded_qty | refund_line_total_amount | return_reason |
|---------|----------------|---------------|-------------------|----------|--------------|------------------------|---------------|
| CoCLUB | 163669365382901624 | heather soulchin | 169.99 | JAEWe-W50459220-FullBed01 | 1 | 169.99 | INADEQUATE_QUALITY |

## 🔧 高级功能

### 自定义查询参数
```python
# 指定特定账户和站点
flattened_data = processor.process_and_save(
    start_date='2025-01-01 00:00:00',
    end_date='2025-01-07 23:59:59',
    sites=['US'],  # 指定站点
    accounts=['CoCLUB', 'Walmart_Keesi'],  # 指定账户
    return_reasons=['INADEQUATE_QUALITY'],  # 指定退款原因
    page_size=100,
    max_pages=10  # 限制最大页数
)
```

### 单页查询
```python
# 获取指定页面的数据
response_data = processor.fetch_return_data(
    start_date='2025-01-01 00:00:00',
    end_date='2025-01-07 23:59:59',
    page=1,
    page_size=50
)
```

### 批量处理大数据
```python
# 获取所有页面数据
all_data = processor.fetch_all_pages(
    start_date='2025-01-01 00:00:00',
    end_date='2025-01-31 23:59:59',
    page_size=100,
    max_pages=None  # 不限制页数
)
```

## 📊 数据库表结构

自动创建的MySQL表包含以下字段：
- **主键**: `id` (自增)
- **唯一索引**: `return_order_id`
- **普通索引**: `customer_order_id`, `account`, `return_order_date`
- **时间戳**: `created_at`, `updated_at`

## ⚙️ 配置说明

### 环境变量配置
```bash
# 设置授权令牌
export WALMART_AUTH_TOKEN="your_token_here"

# 设置数据库连接
export DB_HOST="localhost"
export DB_USER="username"
export DB_PASSWORD="password"
export DB_NAME="walmart_data"
```

### 配置文件
复制 `config_example.py` 为 `config.py` 并修改相应参数。

## 🚨 注意事项

1. **API限制**：请注意API调用频率限制，建议设置适当的请求间隔
2. **数据完整性**：使用 `ON DUPLICATE KEY UPDATE` 确保数据不重复
3. **内存使用**：大量数据处理时注意内存使用，可调整批量大小
4. **网络稳定性**：长时间运行时建议使用稳定的网络连接
5. **令牌有效期**：定期检查和更新授权令牌

## 🐛 错误处理

### 常见问题
1. **授权失败**：检查令牌是否有效和正确
2. **数据库连接失败**：检查数据库配置和网络连接
3. **数据格式错误**：检查API返回的数据格式
4. **内存不足**：减少 `page_size` 或 `batch_size`

### 日志查看
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 性能优化

- **批量插入**：使用批量插入提高数据库写入性能
- **索引优化**：在常用查询字段上建立索引
- **分页处理**：避免一次性加载过多数据
- **连接池**：对于高频使用可考虑使用数据库连接池

## 🔄 更新日志

### v2.0 (优化版)
- ✅ 支持参数化查询
- ✅ 支持分页获取所有数据
- ✅ 支持MySQL数据库存储
- ✅ 完整的错误处理和日志记录
- ✅ 支持批量数据处理
- ✅ 自动数据类型转换和验证

### v1.0 (基础版)
- ✅ 基本的数据扁平化功能
- ✅ Excel和CSV导出
- ✅ 简单的字段映射
