"""
配置文件示例
请复制此文件为 config.py 并修改相应的配置参数
"""

# API配置
API_CONFIG = {
    # 沃尔玛API授权令牌（必填）
    'authorization_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************-U1NPX1NZU19VU0VSIn0.yBJPWPsaUEErJqylInB38BrdwPPqYbWjORFaCtcOTIg',
    
    # API基础URL
    'base_url': 'https://walmart-api.eccang.com/v1/api/order-returns/list'
}

# 数据库配置（可选）
DATABASE_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'sgm',
    'password': 'edfp.md4321',
    'database': 'segmart_erp',
    'autocommit': True,  # 启用自动提交
    'charset': 'utf8mb4'
}

# 查询默认参数
QUERY_CONFIG = {
    # 默认查询日期范围
    'default_start_date': '2025-01-01 00:00:00',
    'default_end_date': '2025-01-07 23:59:59',
    
    # 分页参数
    'default_page_size': 500,  # 优化后的页面大小
    'max_pages_limit': None,  # 不限制页数，获取所有数据

    # 批量处理参数
    'batch_size': 100,  # 数据库批量插入大小
    'request_delay': 1.0,  # 请求间隔（秒），避免频繁请求

    # 重试配置
    'max_retries': 3,  # 最大重试次数
    'retry_delay': 1.0,  # 重试延迟时间（秒）
    'max_consecutive_failures': 3  # 最大连续失败次数
    
    # 默认筛选条件
    'default_sites': [],  # 站点筛选，如 ['US', 'CA']
    'default_accounts': [],  # 账户筛选，如 ['CoCLUB', 'Walmart_Keesi']
    'default_return_reasons': []  # 退款原因筛选
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_path': 'walmart_return_data.log'  # 日志文件路径，None表示不保存到文件
}

# 数据处理配置
DATA_CONFIG = {
    # 是否自动创建数据库表
    'auto_create_table': True,
    
    # 是否使用数据库存储
    'enable_database': True,
    
    # 数据验证规则
    'validation_rules': {
        'required_fields': ['return_order_id', 'customer_order_id'],
        'max_amount': 999999.99,  # 最大金额限制
        'date_format': '%Y-%m-%d %H:%M:%S'
    }
}

# 导出配置
EXPORT_CONFIG = {
    # 默认导出格式
    'default_formats': ['csv', 'excel'],
    
    # 文件命名模板
    'filename_template': 'walmart_return_data_{start_date}_{end_date}',
    
    # Excel配置
    'excel_config': {
        'sheet_name': 'Return Data',
        'index': False,
        'engine': 'openpyxl'
    },
    
    # CSV配置
    'csv_config': {
        'encoding': 'utf-8-sig',
        'index': False
    }
}
