#!/usr/bin/env python3
"""
沃尔玛退款数据处理器 - 安装脚本

此脚本将自动安装所需的依赖包并进行基本配置检查
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """安装依赖包"""
    print("🔧 正在安装依赖包...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    return True

def create_config_file():
    """创建配置文件"""
    print("📝 创建配置文件...")
    
    config_example = Path(__file__).parent / "config_example.py"
    config_file = Path(__file__).parent / "config.py"
    
    if config_file.exists():
        print("⚠️  config.py 已存在，跳过创建")
        return True
    
    if not config_example.exists():
        print("❌ config_example.py 文件不存在")
        return False
    
    try:
        # 复制示例配置文件
        with open(config_example, 'r', encoding='utf-8') as src:
            content = src.read()
        
        with open(config_file, 'w', encoding='utf-8') as dst:
            dst.write(content)
        
        print("✅ 配置文件创建完成: config.py")
        print("📋 请编辑 config.py 文件，设置您的API令牌和数据库配置")
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False

def test_imports():
    """测试导入"""
    print("🧪 测试模块导入...")
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError:
        print("❌ requests 导入失败")
        return False
    
    try:
        import pymysql
        print("✅ pymysql 导入成功")
    except ImportError:
        print("❌ pymysql 导入失败")
        return False
    
    try:
        import pandas
        print("✅ pandas 导入成功")
    except ImportError:
        print("⚠️  pandas 导入失败（可选依赖）")
    
    try:
        import openpyxl
        print("✅ openpyxl 导入成功")
    except ImportError:
        print("⚠️  openpyxl 导入失败（可选依赖）")
    
    return True

def run_basic_test():
    """运行基本测试"""
    print("🚀 运行基本测试...")
    
    try:
        # 尝试导入主模块
        sys.path.insert(0, str(Path(__file__).parent))
        from 沃尔玛退款数据 import WalmartReturnDataProcessor
        
        # 创建测试实例（不使用真实令牌）
        processor = WalmartReturnDataProcessor(
            authorization_token="test_token",
            db_config=None
        )
        
        print("✅ 主模块导入成功")
        print("✅ 处理器实例创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 基本测试失败: {e}")
        return False

def main():
    """主安装流程"""
    print("=" * 60)
    print("🎯 沃尔玛退款数据处理器 - 安装向导")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装依赖包
    if not install_requirements():
        return False
    
    # 测试导入
    if not test_imports():
        return False
    
    # 创建配置文件
    if not create_config_file():
        return False
    
    # 运行基本测试
    if not run_basic_test():
        return False
    
    print("\n" + "=" * 60)
    print("🎉 安装完成！")
    print("=" * 60)
    
    print("\n📋 下一步操作:")
    print("1. 编辑 config.py 文件，设置您的API令牌")
    print("2. 如需使用数据库功能，请配置数据库连接信息")
    print("3. 运行示例: python 优化版使用示例.py")
    print("4. 查看文档: README.md")
    
    print("\n🔗 快速开始:")
    print("```python")
    print("from 沃尔玛退款数据 import WalmartReturnDataProcessor")
    print("processor = WalmartReturnDataProcessor('your_token')")
    print("data = processor.process_and_save('2025-01-01 00:00:00', '2025-01-07 23:59:59')")
    print("```")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
