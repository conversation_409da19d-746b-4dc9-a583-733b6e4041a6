from datetime import datetime, <PERSON><PERSON><PERSON>

def get_first_day_of_months_ago(months):
    today = datetime.now()
    # 计算months个月前的年份和月份
    for _ in range(months):
        today = today.replace(day=1) - timed<PERSON><PERSON>(days=1)
    return today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

# 获取2个月前的第一天起始时间(00:00:00)
START_DATE = get_first_day_of_months_ago(2).strftime('%Y-%m-%d %H:%M:%S')  # 前2个月第一天
# 获取当前日期的结束时间(23:59:59)
END_DATE = datetime.now().strftime('%Y-%m-%d 23:59:59')  # 当前日期结束时间

print(START_DATE)
print(END_DATE)