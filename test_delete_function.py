#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除功能的脚本
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 沃尔玛退款数据 import WalmartReturnDataProcessor, get_first_day_of_months_ago

def test_delete_function():
    """测试删除功能"""
    
    # 配置参数
    AUTHORIZATION_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************-U1NPX1NZU19VU0VSIn0.fVDdrHsTn9R0udn2PYPWK4vGnhyCgNu8_OCZQExUQEA"

    # 数据库配置
    DB_CONFIG = {
        'host': '*************',
        'port': 3306,
        'user': 'sgm',
        'password': 'edfp.md4321',
        'database': 'segmart_erp',
        'autocommit': True,
        'charset': 'utf8mb4'
    }

    # 计算日期范围
    START_DATE = get_first_day_of_months_ago(2).strftime('%Y-%m-%d %H:%M:%S')
    END_DATE = datetime.now().strftime('%Y-%m-%d 23:59:59')

    print(f"测试删除功能")
    print(f"日期范围: {START_DATE} 到 {END_DATE}")
    print("-" * 50)

    try:
        # 创建处理器实例
        processor = WalmartReturnDataProcessor(
            authorization_token=AUTHORIZATION_TOKEN,
            db_config=DB_CONFIG
        )

        # 测试删除功能
        print("正在测试删除功能...")
        deleted_count = processor.delete_data_by_date_range(START_DATE, END_DATE)
        print(f"删除了 {deleted_count} 条记录")

        print("\n测试完成！")

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_delete_function()
